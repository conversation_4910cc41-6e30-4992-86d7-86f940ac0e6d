import axios from 'axios';
import { Platform } from 'react-native';
import * as Sentry from '@sentry/react-native';

// API configuration - Updated to use API v2
export const API_URL = 'http://10.0.0.32:5002/api/v2';
//'http://10.0.0.32:5002/api/v2'; // Local development backend (doesn't work on mobile)
// For production use: 'https://taptrap-backend.vercel.app/api/v2'

/**
 * Centralized API service for TapTrap mobile app
 * Follows existing patterns from contentService.ts
 */

// Create axios instance with base configuration
// Note: Content-Type header is intentionally omitted to let the client set it automatically
// This fixes Android-specific network issues where explicit Content-Type headers cause problems
const api = axios.create({
  baseURL: API_URL,
  timeout: 15000, // 15 second timeout for slower connections
});

// Add request interceptor for logging
api.interceptors.request.use(
  (config) => {
    console.log(`API Request: ${config.method?.toUpperCase()} ${config.url}`);
    return config;
  },
  (error) => {
    console.error('API Request Error:', error);
    Sentry.captureException(error);
    return Promise.reject(error);
  }
);

// Add response interceptor for error handling
api.interceptors.response.use(
  (response) => {
    console.log(`API Response: ${response.status} ${response.config.url}`);
    return response;
  },
  (error) => {
    console.error('API Response Error:', error.response?.status, error.config?.url);
    Sentry.captureException(error);
    return Promise.reject(error);
  }
);

export default api;
