import api from './api';

/**
 * Migration Service
 * 
 * This service handles communication with the backend migration endpoints.
 * It provides functions to manage migration settings for the admin panel.
 */

/**
 * Get current migration settings
 * 
 * @returns {Promise<Object>} - Migration settings object
 */
export const getMigrationSettings = async () => {
  try {
    console.log('Migration Service: Fetching migration settings');
    
    const response = await api.get('/admin/migration');
    
    console.log('Migration Service: Successfully fetched migration settings');
    return response.data;
  } catch (error) {
    console.error('Migration Service: Error fetching migration settings', error);
    throw error;
  }
};

/**
 * Toggle migration on/off
 * 
 * @param {boolean} enabled - Whether to enable migration
 * @returns {Promise<Object>} - Updated migration settings
 */
export const toggleMigration = async (enabled) => {
  try {
    console.log(`Migration Service: Toggling migration to ${enabled ? 'enabled' : 'disabled'}`);
    
    const response = await api.post('/admin/migration/toggle', {
      enabled
    });
    
    console.log('Migration Service: Successfully toggled migration');
    return response.data;
  } catch (error) {
    console.error('Migration Service: Error toggling migration', error);
    throw error;
  }
};

/**
 * Update migration message
 * 
 * @param {Object} migrationMessage - New migration message object
 * @param {string} migrationMessage.field_en - English message
 * @param {string} migrationMessage.field_es - Spanish message
 * @param {string} migrationMessage.field_dom - Dominican Spanish message
 * @returns {Promise<Object>} - Updated migration settings
 */
export const updateMigrationMessage = async (migrationMessage) => {
  try {
    console.log('Migration Service: Updating migration message');
    
    const response = await api.put('/admin/migration/message', {
      migrationMessage
    });
    
    console.log('Migration Service: Successfully updated migration message');
    return response.data;
  } catch (error) {
    console.error('Migration Service: Error updating migration message', error);
    throw error;
  }
};

/**
 * Get migration status (lightweight)
 * 
 * @returns {Promise<Object>} - Migration status object
 */
export const getMigrationStatus = async () => {
  try {
    console.log('Migration Service: Fetching migration status');
    
    const response = await api.get('/admin/migration/status');
    
    console.log('Migration Service: Successfully fetched migration status');
    return response.data;
  } catch (error) {
    console.error('Migration Service: Error fetching migration status', error);
    throw error;
  }
};
